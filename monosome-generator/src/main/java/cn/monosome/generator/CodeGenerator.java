package cn.monosome.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.util.Collections;
import java.util.Scanner;

/**
 * MyBatis Plus代码生成器
 *
 * <AUTHOR>
 */
public class CodeGenerator {

    public static void main(String[] args) {
        // 获取用户输入
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("请输入数据库连接信息：");
        System.out.print("数据库URL（默认：************************************）：");
        String url = scanner.nextLine();
        if (url.trim().isEmpty()) {
            url = "*************************************************************************************************************************************************";
        }
        
        System.out.print("数据库用户名（默认：root）：");
        String username = scanner.nextLine();
        if (username.trim().isEmpty()) {
            username = "root";
        }
        
        System.out.print("数据库密码（默认：123456）：");
        String password = scanner.nextLine();
        if (password.trim().isEmpty()) {
            password = "123456";
        }
        
        System.out.print("请输入表名（多个表名用逗号分隔）：");
        String tableNames = scanner.nextLine();
        
        System.out.print("请选择模块（1-管理端 2-用户端）：");
        String moduleChoice = scanner.nextLine();
        
        String moduleName;
        String packageName;
        if ("1".equals(moduleChoice)) {
            moduleName = "manager";
            packageName = "cn.monosome.manager";
        } else {
            moduleName = "using";
            packageName = "cn.monosome.using";
        }
        
        // 获取项目路径
        String projectPath = System.getProperty("user.dir");
        String outputDir = projectPath + "/monosome-" + moduleName + "/src/main/java";
        String mapperXmlPath = projectPath + "/monosome-" + moduleName + "/src/main/resources/mapper";
        
        FastAutoGenerator.create(url, username, password)
                .globalConfig(builder -> {
                    builder.author("monosome") // 设置作者
                            .enableSwagger() // 开启 swagger 模式
                            .outputDir(outputDir) // 指定输出目录
                            .dateType(DateType.TIME_PACK) // 时间策略
                            .commentDate("yyyy-MM-dd"); // 注释日期
                })
                .packageConfig(builder -> {
                    builder.parent(packageName) // 设置父包名
                            .pathInfo(Collections.singletonMap(OutputFile.xml, mapperXmlPath)); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude(tableNames.split(",")) // 设置需要生成的表名
                            .addTablePrefix("t_", "c_") // 设置过滤表前缀
                            .entityBuilder()
                            .enableLombok() // 开启 lombok 模式
                            .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                            .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
                            .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                            .idType(IdType.ASSIGN_ID) // 全局主键类型
                            .addSuperEntityColumns("id", "create_time", "update_time", "create_by", "update_by", "deleted", "version") // 添加父类公共字段
                            .addTableFills(new Column("create_time", FieldFill.INSERT)) // 基于数据库字段填充
                            .addTableFills(new Column("update_time", FieldFill.INSERT_UPDATE))
                            .addTableFills(new Column("create_by", FieldFill.INSERT))
                            .addTableFills(new Column("update_by", FieldFill.INSERT_UPDATE))
                            .addTableFills(new Column("deleted", FieldFill.INSERT))
                            .addTableFills(new Column("version", FieldFill.INSERT))
                            .superClass("cn.monosome.common.base.BaseEntity") // 设置父类
                            .controllerBuilder()
                            .enableRestStyle() // 开启生成@RestController 控制器
                            .superClass("cn.monosome.common.base.BaseController") // 设置父类
                            .serviceBuilder()
                            .formatServiceFileName("I%sService") // 格式化 service 接口文件名称
                            .formatServiceImplFileName("%sServiceImpl") // 格式化 service 实现类文件名称
                            .mapperBuilder()
                            .enableMapperAnnotation(); // 开启 @Mapper 注解
                })
                .execute();
        
        System.out.println("代码生成完成！");
        System.out.println("输出目录：" + outputDir);
        System.out.println("Mapper XML目录：" + mapperXmlPath);
    }
}
