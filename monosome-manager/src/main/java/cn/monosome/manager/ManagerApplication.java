package cn.monosome.manager;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 管理端启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "cn.monosome")
@MapperScan({"cn.monosome.manager.mapper", "cn.monosome.using.mapper"})
public class ManagerApplication {

    public static void main(String[] args) {
        SpringApplication.run(ManagerApplication.class, args);
        System.out.println("=================================");
        System.out.println("管理端启动成功！");
        System.out.println("接口文档地址：http://localhost:8080/doc.html");
        System.out.println("=================================");
    }
}
