package cn.monosome.manager.controller;

import cn.monosome.common.base.BaseController;
import cn.monosome.common.result.Result;
import cn.monosome.using.entity.User;
import cn.monosome.using.entity.Product;
import cn.monosome.manager.service.IUserManageService;
import cn.monosome.manager.service.IProductManageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户管理控制器（管理端管理用户端数据）
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/manage/user")
@Tag(name = "用户管理（管理端）")
public class UserManageController extends BaseController {

    @Autowired
    private IUserManageService userManageService;

    @GetMapping("/list")
    @Operation(summary = "用户列表")
    public Result<Page<User>> list(@RequestParam(defaultValue = "1") Integer current,
                                   @RequestParam(defaultValue = "10") Integer size,
                                   @RequestParam(required = false) String username,
                                   @RequestParam(required = false) String nickname) {
        Page<User> page = new Page<>(current, size);
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        
        if (username != null && !username.trim().isEmpty()) {
            queryWrapper.like(User::getUsername, username);
        }
        if (nickname != null && !nickname.trim().isEmpty()) {
            queryWrapper.like(User::getNickname, nickname);
        }
        
        Page<User> result = userManageService.page(page, queryWrapper);
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取用户详情")
    public Result<User> getById(@PathVariable Long id) {
        User user = userManageService.getById(id);
        return user != null ? Result.success(user) : Result.error("用户不存在");
    }

    @PostMapping
    @Operation(summary = "新增用户")
    public Result<String> save(@RequestBody @Validated User user) {
        boolean success = userManageService.register(user);
        return success ? Result.success("新增成功") : Result.error("新增失败");
    }

    @PutMapping
    @Operation(summary = "更新用户")
    public Result<String> update(@RequestBody @Validated User user) {
        boolean success = userManageService.updateById(user);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户")
    public Result<String> delete(@PathVariable Long id) {
        boolean success = userManageService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新用户状态")
    public Result<String> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        User user = new User();
        user.setId(id);
        user.setStatus(status);
        boolean success = userManageService.updateById(user);
        return success ? Result.success("状态更新成功") : Result.error("状态更新失败");
    }
}
