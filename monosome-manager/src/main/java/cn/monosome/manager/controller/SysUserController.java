package cn.monosome.manager.controller;

import cn.monosome.common.base.BaseController;
import cn.monosome.common.result.Result;
import cn.monosome.manager.entity.SysUser;
import cn.monosome.manager.service.ISysUserService;
import cn.monosome.manager.vo.LoginRequest;
import cn.monosome.manager.vo.LoginResponse;
import cn.monosome.manager.vo.ChangePasswordRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统用户控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/sys/user")
@Tag(name = "系统用户管理")
public class SysUserController extends BaseController {

    @Autowired
    private ISysUserService sysUserService;

    @PostMapping("/login")
    @Operation(summary = "用户登录")
    public Result<LoginResponse> login(@RequestBody @Validated LoginRequest request) {
        String token = sysUserService.login(request.getUsername(), request.getPassword());
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        return Result.success("登录成功", response);
    }

    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public Result<String> register(@RequestBody @Validated SysUser sysUser) {
        boolean success = sysUserService.register(sysUser);
        return success ? Result.success("注册成功") : Result.error("注册失败");
    }

    @PostMapping("/changePassword")
    @Operation(summary = "修改密码")
    public Result<String> changePassword(@RequestBody @Validated ChangePasswordRequest request) {
        boolean success = sysUserService.changePassword(request.getUserId(), request.getOldPassword(), request.getNewPassword());
        return success ? Result.success("密码修改成功") : Result.error("密码修改失败");
    }

    @GetMapping("/list")
    @Operation(summary = "用户列表")
    public Result<Page<SysUser>> list(@RequestParam(defaultValue = "1") Integer current,
                                      @RequestParam(defaultValue = "10") Integer size,
                                      @RequestParam(required = false) String username,
                                      @RequestParam(required = false) String realName) {
        Page<SysUser> page = new Page<>(current, size);
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        
        if (username != null && !username.trim().isEmpty()) {
            queryWrapper.like(SysUser::getUsername, username);
        }
        if (realName != null && !realName.trim().isEmpty()) {
            queryWrapper.like(SysUser::getRealName, realName);
        }
        
        Page<SysUser> result = sysUserService.page(page, queryWrapper);
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取用户详情")
    public Result<SysUser> getById(@PathVariable Long id) {
        SysUser user = sysUserService.getById(id);
        return user != null ? Result.success(user) : Result.error("用户不存在");
    }

    @PostMapping
    @Operation(summary = "新增用户")
    public Result<String> save(@RequestBody @Validated SysUser sysUser) {
        boolean success = sysUserService.register(sysUser);
        return success ? Result.success("新增成功") : Result.error("新增失败");
    }

    @PutMapping
    @Operation(summary = "更新用户")
    public Result<String> update(@RequestBody @Validated SysUser sysUser) {
        boolean success = sysUserService.updateById(sysUser);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户")
    public Result<String> delete(@PathVariable Long id) {
        boolean success = sysUserService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }
}
