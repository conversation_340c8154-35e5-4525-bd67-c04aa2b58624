package cn.monosome.manager.controller;

import cn.monosome.common.base.BaseController;
import cn.monosome.common.result.Result;
import cn.monosome.using.entity.Product;
import cn.monosome.manager.service.IProductManageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 商品管理控制器（管理端管理商品数据）
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/manage/product")
@Tag(name = "商品管理（管理端）")
public class ProductManageController extends BaseController {

    @Autowired
    private IProductManageService productManageService;

    @GetMapping("/list")
    @Operation(summary = "商品列表")
    public Result<Page<Product>> list(@RequestParam(defaultValue = "1") Integer current,
                                      @RequestParam(defaultValue = "10") Integer size,
                                      @RequestParam(required = false) String name,
                                      @RequestParam(required = false) Long categoryId,
                                      @RequestParam(required = false) Integer status) {
        Page<Product> page = new Page<>(current, size);
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        
        if (name != null && !name.trim().isEmpty()) {
            queryWrapper.like(Product::getName, name);
        }
        if (categoryId != null) {
            queryWrapper.eq(Product::getCategoryId, categoryId);
        }
        if (status != null) {
            queryWrapper.eq(Product::getStatus, status);
        }
        
        queryWrapper.orderByDesc(Product::getSort, Product::getCreateTime);
        
        Page<Product> result = productManageService.page(page, queryWrapper);
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取商品详情")
    public Result<Product> getById(@PathVariable Long id) {
        Product product = productManageService.getById(id);
        return product != null ? Result.success(product) : Result.error("商品不存在");
    }

    @PostMapping
    @Operation(summary = "新增商品")
    public Result<String> save(@RequestBody @Validated Product product) {
        boolean success = productManageService.save(product);
        return success ? Result.success("新增成功") : Result.error("新增失败");
    }

    @PutMapping
    @Operation(summary = "更新商品")
    public Result<String> update(@RequestBody @Validated Product product) {
        boolean success = productManageService.updateById(product);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除商品")
    public Result<String> delete(@PathVariable Long id) {
        boolean success = productManageService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新商品状态")
    public Result<String> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        Product product = new Product();
        product.setId(id);
        product.setStatus(status);
        boolean success = productManageService.updateById(product);
        return success ? Result.success("状态更新成功") : Result.error("状态更新失败");
    }
}
