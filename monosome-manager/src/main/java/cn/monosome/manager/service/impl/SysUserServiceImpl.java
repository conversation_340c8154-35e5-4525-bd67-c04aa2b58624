package cn.monosome.manager.service.impl;

import cn.hutool.crypto.digest.BCrypt;
import cn.monosome.common.result.ResultCode;
import cn.monosome.common.utils.JwtUtil;
import cn.monosome.manager.entity.SysUser;
import cn.monosome.manager.mapper.SysUserMapper;
import cn.monosome.manager.service.ISysUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 系统用户服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Override
    public SysUser selectUserByUsername(String username) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, username);
        return this.getOne(queryWrapper);
    }

    @Override
    public String login(String username, String password) {
        // 查询用户
        SysUser user = selectUserByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证密码
        if (!BCrypt.checkpw(password, user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 检查用户状态
        if (user.getStatus() != null && user.getStatus() == 1) {
            throw new RuntimeException("用户已被禁用");
        }

        // 生成token
        return JwtUtil.createToken(user.getId(), user.getUsername());
    }

    @Override
    public boolean register(SysUser sysUser) {
        // 检查用户名是否已存在
        SysUser existUser = selectUserByUsername(sysUser.getUsername());
        if (existUser != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 密码加密
        sysUser.setPassword(BCrypt.hashpw(sysUser.getPassword(), BCrypt.gensalt()));
        
        // 设置默认状态
        sysUser.setStatus(0);

        return this.save(sysUser);
    }

    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        // 查询用户
        SysUser user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证旧密码
        if (!BCrypt.checkpw(oldPassword, user.getPassword())) {
            throw new RuntimeException("旧密码错误");
        }

        // 更新密码
        user.setPassword(BCrypt.hashpw(newPassword, BCrypt.gensalt()));
        return this.updateById(user);
    }
}
