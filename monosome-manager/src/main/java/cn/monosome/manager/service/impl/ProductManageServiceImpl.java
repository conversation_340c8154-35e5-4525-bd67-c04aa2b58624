package cn.monosome.manager.service.impl;

import cn.monosome.using.entity.Product;
import cn.monosome.using.mapper.ProductMapper;
import cn.monosome.manager.service.IProductManageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品管理服务实现类（管理端管理商品数据）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductManageServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductManageService {

}
