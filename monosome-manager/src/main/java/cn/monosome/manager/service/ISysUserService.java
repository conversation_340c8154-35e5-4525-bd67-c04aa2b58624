package cn.monosome.manager.service;

import cn.monosome.manager.entity.SysUser;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 系统用户服务接口
 *
 * <AUTHOR>
 */
public interface ISysUserService extends IService<SysUser> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectUserByUsername(String username);

    /**
     * 用户登录
     *
     * @param username 用户名
     * @param password 密码
     * @return token
     */
    String login(String username, String password);

    /**
     * 用户注册
     *
     * @param sysUser 用户信息
     * @return 是否成功
     */
    boolean register(SysUser sysUser);

    /**
     * 修改密码
     *
     * @param userId      用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);
}
