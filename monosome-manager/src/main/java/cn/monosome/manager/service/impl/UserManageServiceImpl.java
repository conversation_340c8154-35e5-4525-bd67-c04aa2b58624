package cn.monosome.manager.service.impl;

import cn.hutool.crypto.digest.BCrypt;
import cn.monosome.using.entity.User;
import cn.monosome.using.mapper.UserMapper;
import cn.monosome.manager.service.IUserManageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户管理服务实现类（管理端管理用户端数据）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserManageServiceImpl extends ServiceImpl<UserMapper, User> implements IUserManageService {

    @Override
    public boolean register(User user) {
        // 检查用户名是否已存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, user.getUsername());
        User existUser = this.getOne(queryWrapper);
        if (existUser != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 密码加密
        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            user.setPassword(BCrypt.hashpw(user.getPassword(), BCrypt.gensalt()));
        }
        
        // 设置默认状态
        if (user.getStatus() == null) {
            user.setStatus(0);
        }

        return this.save(user);
    }
}
