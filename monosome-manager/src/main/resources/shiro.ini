[main]
# 配置简单的Realm
authc.loginUrl = /login

[users]
# 临时用户配置，格式：用户名 = 密码, 角色
admin = 123456, admin
user = 123456, user

[roles]
# 角色权限配置
admin = *
user = user:*

[urls]
# URL权限配置
/static/** = anon
/css/** = anon
/js/** = anon
/images/** = anon
/favicon.ico = anon
/doc.html = anon
/webjars/** = anon
/v3/api-docs/** = anon
/swagger-ui/** = anon
/sys/user/login = anon
/user/login = anon
/user/register = anon
/actuator/** = anon
/** = anon
