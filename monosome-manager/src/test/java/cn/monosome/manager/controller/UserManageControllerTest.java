package cn.monosome.manager.controller;

import cn.monosome.using.entity.User;
import cn.monosome.manager.service.IUserManageService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 用户管理控制器测试
 *
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureMockMvc
class UserManageControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private IUserManageService userManageService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testGetUserList() throws Exception {
        mockMvc.perform(get("/manage/user/list")
                .param("current", "1")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testCreateUser() throws Exception {
        User user = new User();
        user.setUsername("testuser" + System.currentTimeMillis());
        user.setPassword("123456");
        user.setNickname("测试用户");
        user.setPhone("13800138000");
        user.setEmail("<EMAIL>");

        mockMvc.perform(post("/manage/user")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(user)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testUpdateUserStatus() throws Exception {
        // 先创建一个用户
        User user = new User();
        user.setUsername("statustest" + System.currentTimeMillis());
        user.setPassword("123456");
        user.setNickname("状态测试用户");
        userManageService.register(user);

        // 更新状态
        mockMvc.perform(put("/manage/user/" + user.getId() + "/status")
                .param("status", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
