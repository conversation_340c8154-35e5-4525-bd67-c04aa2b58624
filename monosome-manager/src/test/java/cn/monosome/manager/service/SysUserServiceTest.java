package cn.monosome.manager.service;

import cn.monosome.manager.entity.SysUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 系统用户服务测试
 *
 * <AUTHOR>
 */
@SpringBootTest
class SysUserServiceTest {

    @Autowired
    private ISysUserService sysUserService;

    @Test
    void testLogin() {
        // 测试登录
        String token = sysUserService.login("admin", "123456");
        assertNotNull(token);
        System.out.println("登录成功，Token: " + token);
    }

    @Test
    void testRegister() {
        // 测试注册
        SysUser user = new SysUser();
        user.setUsername("testuser" + System.currentTimeMillis());
        user.setPassword("123456");
        user.setRealName("测试用户");
        user.setPhone("13800138000");
        user.setEmail("<EMAIL>");
        
        boolean result = sysUserService.register(user);
        assertTrue(result);
        System.out.println("注册成功，用户ID: " + user.getId());
    }

    @Test
    void testSelectUserByUsername() {
        // 测试根据用户名查询用户
        SysUser user = sysUserService.selectUserByUsername("admin");
        assertNotNull(user);
        assertEquals("admin", user.getUsername());
        System.out.println("查询用户成功: " + user.getRealName());
    }

    @Test
    void testChangePassword() {
        // 先创建一个测试用户
        SysUser user = new SysUser();
        user.setUsername("changepasstest" + System.currentTimeMillis());
        user.setPassword("123456");
        user.setRealName("修改密码测试用户");
        sysUserService.register(user);
        
        // 测试修改密码
        boolean result = sysUserService.changePassword(user.getId(), "123456", "newpassword");
        assertTrue(result);
        System.out.println("密码修改成功");
    }
}
