@echo off
chcp 65001 >nul
setlocal

echo ==========================================
echo 开始构建 Monosome 项目
echo ==========================================

:: 清理并编译
echo 1. 清理并编译项目...
call mvn clean compile

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

:: 运行测试
echo 2. 运行测试...
call mvn test

if %errorlevel% neq 0 (
    echo 测试失败！
    pause
    exit /b 1
)

:: 打包
echo 3. 打包项目...
call mvn package -DskipTests

if %errorlevel% neq 0 (
    echo 打包失败！
    pause
    exit /b 1
)

:: 创建发布目录
echo 4. 创建发布目录...
if exist release rmdir /s /q release
mkdir release

:: 复制管理端打包文件
for %%f in (target\monosome-manager-*.tar.gz) do (
    if exist "%%f" (
        copy "%%f" release\
        echo 管理端tar.gz文件已复制到 release 目录
    )
)

for %%f in (target\monosome-manager-*.zip) do (
    if exist "%%f" (
        copy "%%f" release\
        echo 管理端zip文件已复制到 release 目录
    )
)

:: 复制用户端打包文件
for %%f in (target\monosome-using-*.tar.gz) do (
    if exist "%%f" (
        copy "%%f" release\
        echo 用户端tar.gz文件已复制到 release 目录
    )
)

for %%f in (target\monosome-using-*.zip) do (
    if exist "%%f" (
        copy "%%f" release\
        echo 用户端zip文件已复制到 release 目录
    )
)

:: 复制SQL脚本
xcopy sql release\sql\ /e /i /y
echo SQL脚本已复制到 release 目录

:: 复制README
copy README.md release\
echo README文档已复制到 release 目录

echo ==========================================
echo 构建完成！
echo 发布文件位于 release 目录中：
dir release
echo ==========================================

echo.
echo 部署说明：
echo 1. 解压对应的tar.gz或zip文件
echo 2. 执行sql/monosome.sql初始化数据库
echo 3. 修改config/application.yml中的数据库和Redis配置
echo 4. 运行bin/start.bat启动应用
echo.
echo 访问地址：
echo 管理端: http://localhost:8080
echo 用户端: http://localhost:8081
echo 管理端API文档: http://localhost:8080/doc.html
echo 用户端API文档: http://localhost:8081/doc.html

pause
