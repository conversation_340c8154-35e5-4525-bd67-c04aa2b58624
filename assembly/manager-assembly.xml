<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.0 http://maven.apache.org/xsd/assembly-2.1.0.xsd">
    <id>manager</id>
    <formats>
        <format>tar.gz</format>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <baseDirectory>monosome-manager</baseDirectory>

    <fileSets>
        <!-- 包含jar文件 -->
        <fileSet>
            <directory>monosome-manager/target</directory>
            <outputDirectory>lib</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>

        <!-- 包含配置文件 -->
        <fileSet>
            <directory>monosome-manager/src/main/resources</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>application*.yml</include>
                <include>application*.yaml</include>
                <include>application*.properties</include>
                <include>logback*.xml</include>
            </includes>
        </fileSet>

        <!-- 包含启动脚本 -->
        <fileSet>
            <directory>assembly/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
            <includes>
                <include>*.sh</include>
                <include>*.bat</include>
            </includes>
        </fileSet>

        <!-- 创建日志目录 -->
        <fileSet>
            <directory>assembly</directory>
            <outputDirectory>logs</outputDirectory>
            <excludes>
                <exclude>**/*</exclude>
            </excludes>
        </fileSet>
    </fileSets>
</assembly>
