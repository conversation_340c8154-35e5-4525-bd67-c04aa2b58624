#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# 应用名称
APP_NAME="monosome"

# PID文件路径
PID_FILE="$BASE_DIR/$APP_NAME.pid"

if [ ! -f "$PID_FILE" ]; then
    echo "$APP_NAME 未运行"
    exit 1
fi

PID=$(cat "$PID_FILE")

if ! ps -p $PID > /dev/null 2>&1; then
    echo "$APP_NAME 未运行"
    rm -f "$PID_FILE"
    exit 1
fi

echo "正在停止 $APP_NAME (PID: $PID)..."

# 发送TERM信号
kill $PID

# 等待进程结束
for i in {1..30}; do
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "$APP_NAME 已停止"
        rm -f "$PID_FILE"
        exit 0
    fi
    sleep 1
done

# 如果进程仍在运行，强制杀死
echo "强制停止 $APP_NAME..."
kill -9 $PID
rm -f "$PID_FILE"
echo "$APP_NAME 已强制停止"
