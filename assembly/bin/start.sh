#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# 应用名称
APP_NAME="monosome"

# Java参数
JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError"

# 配置文件路径
CONFIG_DIR="$BASE_DIR/config"

# 日志目录
LOG_DIR="$BASE_DIR/logs"

# JAR文件路径
JAR_FILE=$(find "$BASE_DIR/lib" -name "*.jar" | head -1)

if [ -z "$JAR_FILE" ]; then
    echo "错误: 未找到JAR文件"
    exit 1
fi

# 检查是否已经运行
PID_FILE="$BASE_DIR/$APP_NAME.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        echo "$APP_NAME 已经在运行中 (PID: $PID)"
        exit 1
    else
        rm -f "$PID_FILE"
    fi
fi

# 创建日志目录
mkdir -p "$LOG_DIR"

echo "正在启动 $APP_NAME..."

# 启动应用
nohup java $JAVA_OPTS \
    -Dspring.config.location="$CONFIG_DIR/" \
    -Dlogging.file.path="$LOG_DIR" \
    -jar "$JAR_FILE" \
    > "$LOG_DIR/startup.log" 2>&1 &

# 保存PID
echo $! > "$PID_FILE"

echo "$APP_NAME 启动成功 (PID: $!)"
echo "日志文件: $LOG_DIR/startup.log"
