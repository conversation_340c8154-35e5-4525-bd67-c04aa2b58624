@echo off
setlocal

:: 获取脚本所在目录
set SCRIPT_DIR=%~dp0
set BASE_DIR=%SCRIPT_DIR%..

:: 应用名称
set APP_NAME=monosome

:: Java参数
set JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError

:: 配置文件路径
set CONFIG_DIR=%BASE_DIR%\config

:: 日志目录
set LOG_DIR=%BASE_DIR%\logs

:: 查找JAR文件
for %%f in ("%BASE_DIR%\lib\*.jar") do set JAR_FILE=%%f

if not exist "%JAR_FILE%" (
    echo 错误: 未找到JAR文件
    pause
    exit /b 1
)

:: 检查是否已经运行
set PID_FILE=%BASE_DIR%\%APP_NAME%.pid
if exist "%PID_FILE%" (
    set /p PID=<"%PID_FILE%"
    tasklist /FI "PID eq !PID!" 2>nul | find /I "java.exe" >nul
    if !errorlevel! equ 0 (
        echo %APP_NAME% 已经在运行中 (PID: !PID!)
        pause
        exit /b 1
    ) else (
        del "%PID_FILE%" 2>nul
    )
)

:: 创建日志目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

echo 正在启动 %APP_NAME%...

:: 启动应用
start /B java %JAVA_OPTS% ^
    -Dspring.config.location="%CONFIG_DIR%/" ^
    -Dlogging.file.path="%LOG_DIR%" ^
    -jar "%JAR_FILE%" ^
    > "%LOG_DIR%\startup.log" 2>&1

:: 获取进程ID（Windows下比较复杂，这里简化处理）
echo %APP_NAME% 启动成功
echo 日志文件: %LOG_DIR%\startup.log

pause
