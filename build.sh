#!/bin/bash

echo "=========================================="
echo "开始构建 Monosome 项目"
echo "=========================================="

# 清理并编译
echo "1. 清理并编译项目..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

# 运行测试
echo "2. 运行测试..."
mvn test

if [ $? -ne 0 ]; then
    echo "测试失败！"
    exit 1
fi

# 打包
echo "3. 打包项目..."
mvn package -DskipTests

if [ $? -ne 0 ]; then
    echo "打包失败！"
    exit 1
fi

# 创建发布目录
echo "4. 创建发布目录..."
rm -rf release
mkdir -p release

# 复制管理端打包文件
if [ -f "target/monosome-manager-*.tar.gz" ]; then
    cp target/monosome-manager-*.tar.gz release/
    echo "管理端打包文件已复制到 release 目录"
fi

if [ -f "target/monosome-manager-*.zip" ]; then
    cp target/monosome-manager-*.zip release/
fi

# 复制用户端打包文件
if [ -f "target/monosome-using-*.tar.gz" ]; then
    cp target/monosome-using-*.tar.gz release/
    echo "用户端打包文件已复制到 release 目录"
fi

if [ -f "target/monosome-using-*.zip" ]; then
    cp target/monosome-using-*.zip release/
fi

# 复制SQL脚本
cp -r sql release/
echo "SQL脚本已复制到 release 目录"

# 复制README
cp README.md release/
echo "README文档已复制到 release 目录"

echo "=========================================="
echo "构建完成！"
echo "发布文件位于 release 目录中："
ls -la release/
echo "=========================================="

echo ""
echo "部署说明："
echo "1. 解压对应的tar.gz或zip文件"
echo "2. 执行sql/monosome.sql初始化数据库"
echo "3. 修改config/application.yml中的数据库和Redis配置"
echo "4. 运行bin/start.sh启动应用"
echo ""
echo "访问地址："
echo "管理端: http://localhost:8080"
echo "用户端: http://localhost:8081"
echo "管理端API文档: http://localhost:8080/doc.html"
echo "用户端API文档: http://localhost:8081/doc.html"
