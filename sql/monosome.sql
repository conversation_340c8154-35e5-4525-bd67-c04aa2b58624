-- 创建数据库
CREATE DATABASE IF NOT EXISTS `monosome` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `monosome`;

-- 系统用户表
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `id` bigint NOT NULL COMMENT '用户ID',
  `username` varchar(30) NOT NULL COMMENT '用户账号',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `real_name` varchar(30) NOT NULL COMMENT '用户姓名',
  `phone` varchar(11) DEFAULT '' COMMENT '手机号码',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `gender` tinyint DEFAULT '0' COMMENT '用户性别（0女 1男 2未知）',
  `status` tinyint DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `version` int DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- 系统角色表
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `id` bigint NOT NULL COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `status` tinyint DEFAULT '0' COMMENT '角色状态（0正常 1停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `version` int DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_key` (`role_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统角色表';

-- 用户表
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` bigint NOT NULL COMMENT '用户ID',
  `username` varchar(30) NOT NULL COMMENT '用户名',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `nickname` varchar(30) DEFAULT '' COMMENT '昵称',
  `phone` varchar(11) DEFAULT '' COMMENT '手机号码',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `gender` tinyint DEFAULT '0' COMMENT '用户性别（0女 1男 2未知）',
  `birthday` varchar(10) DEFAULT '' COMMENT '生日',
  `status` tinyint DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `version` int DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 商品表
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `id` bigint NOT NULL COMMENT '商品ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `image` varchar(200) DEFAULT '' COMMENT '商品图片',
  `category_id` bigint DEFAULT NULL COMMENT '商品分类ID',
  `stock` int DEFAULT '0' COMMENT '库存数量',
  `status` tinyint DEFAULT '0' COMMENT '状态（0下架 1上架）',
  `sort` int DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `version` int DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 插入测试数据

-- 系统用户测试数据
INSERT INTO `sys_user` VALUES 
(1, 'admin', '$2a$10$7JB720yubVSOfvVMe6/YqO4wkhWGEn67XVb1kLqDqHdWuVv/HLjve', '系统管理员', '18888888888', '<EMAIL>', '', 1, 0, '管理员', NOW(), NOW(), 1, 1, 0, 1),
(2, 'test', '$2a$10$7JB720yubVSOfvVMe6/YqO4wkhWGEn67XVb1kLqDqHdWuVv/HLjve', '测试用户', '18999999999', '<EMAIL>', '', 0, 0, '测试用户', NOW(), NOW(), 1, 1, 0, 1);

-- 系统角色测试数据
INSERT INTO `sys_role` VALUES 
(1, '超级管理员', 'admin', 1, 0, '超级管理员', NOW(), NOW(), 1, 1, 0, 1),
(2, '普通用户', 'common', 2, 0, '普通用户', NOW(), NOW(), 1, 1, 0, 1);

-- 用户测试数据
INSERT INTO `user` VALUES 
(1, 'user001', '$2a$10$7JB720yubVSOfvVMe6/YqO4wkhWGEn67XVb1kLqDqHdWuVv/HLjve', '张三', '13888888888', '<EMAIL>', '', 1, '1990-01-01', 0, NOW(), NOW(), 1, 1, 0, 1),
(2, 'user002', '$2a$10$7JB720yubVSOfvVMe6/YqO4wkhWGEn67XVb1kLqDqHdWuVv/HLjve', '李四', '13999999999', '<EMAIL>', '', 0, '1992-05-15', 0, NOW(), NOW(), 1, 1, 0, 1);

-- 商品测试数据
INSERT INTO `product` VALUES 
(1, 'iPhone 15 Pro', '苹果最新款手机，性能强劲，拍照出色', 7999.00, '', 1, 100, 1, 1, NOW(), NOW(), 1, 1, 0, 1),
(2, 'MacBook Pro', '苹果笔记本电脑，适合开发和设计', 12999.00, '', 1, 50, 1, 2, NOW(), NOW(), 1, 1, 0, 1),
(3, 'AirPods Pro', '苹果无线耳机，降噪效果出色', 1999.00, '', 2, 200, 1, 3, NOW(), NOW(), 1, 1, 0, 1);

-- 注意：密码都是 123456
