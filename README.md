# Monosome - Spring Boot快速开发脚手架

## 项目简介

Monosome是一个基于Spring Boot的快速开发脚手架，采用前后端分离架构，专注于后端服务开发。项目集成了常用的开发组件和工具，提供了完整的权限管理、代码生成、监控等功能。

## 技术栈

- **基础框架**: Spring Boot 2.7.18
- **数据访问**: MyBatis Plus 3.5.5
- **数据库**: MySQL 8.0+
- **连接池**: HikariCP + Druid
- **缓存**: Redis
- **权限控制**: Shiro + JWT
- **接口文档**: Swagger + Knife4j
- **监控**: Spring Boot Admin
- **工具类**: Hutool
- **JSON处理**: FastJSON2
- **Excel处理**: EasyExcel

## 项目结构

```
monosome/
├── monosome-common/          # 公共模块
│   ├── config/              # 配置类
│   ├── result/              # 统一返回结果
│   ├── base/                # 基础类
│   └── utils/               # 工具类
├── monosome-manager/         # 后台管理端
│   ├── controller/          # 控制器
│   ├── service/             # 服务层
│   ├── entity/              # 实体类
│   └── mapper/              # 数据访问层
├── monosome-using/           # 用户端
│   ├── controller/          # 控制器
│   ├── service/             # 服务层
│   ├── entity/              # 实体类
│   └── mapper/              # 数据访问层
├── monosome-generator/       # 代码生成器
├── assembly/                 # 打包配置
├── sql/                      # 数据库脚本
└── README.md
```

## 主要特性

### 1. Maven多模块架构
- 模块化设计，便于维护和扩展
- 公共模块复用，减少代码重复

### 2. 数据访问层
- 集成MyBatis Plus，简化CRUD操作
- 支持多数据源配置
- 自动填充创建时间、更新时间等字段
- 支持逻辑删除和乐观锁

### 3. 权限控制
- 基于Shiro + JWT的权限控制
- 管理端和用户端独立的登录系统
- 支持角色权限管理

### 4. 接口文档
- 集成Knife4j，自动生成API文档
- 支持在线调试
- 分组管理（管理端/用户端）

### 5. 缓存支持
- 集成Redis缓存
- 提供Redis工具类，简化操作

### 6. 监控功能
- 集成Spring Boot Admin
- 实时监控应用状态
- 集成Druid监控，支持SQL性能分析

### 7. 代码生成器
- 基于MyBatis Plus Generator
- 支持自定义模板
- 一键生成Entity、Mapper、Service、Controller

### 8. 打包部署
- 使用Assembly插件
- 支持不同环境配置
- 提供启动、停止、重启脚本
- 配置文件外部化

### 9. 工具类集成
- Redis操作工具类
- Excel导入导出工具类
- JWT工具类
- 其他常用工具类

## 快速开始

### 1. 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 2. 数据库初始化
```sql
-- 执行sql/monosome.sql脚本
mysql -u root -p < sql/monosome.sql
```

### 3. 修改配置
修改各模块的`application.yml`文件中的数据库和Redis连接信息。

### 4. 编译打包
```bash
# 编译整个项目
mvn clean compile

# 打包
mvn clean package
```

### 5. 启动应用

#### 开发环境
```bash
# 启动管理端
cd monosome-manager
mvn spring-boot:run

# 启动用户端
cd monosome-using
mvn spring-boot:run
```

#### 生产环境
```bash
# 使用打包后的脚本启动
cd target/monosome-manager/bin
./start.sh

cd target/monosome-using/bin
./start.sh
```

### 6. 访问应用
- 管理端: http://localhost:8080
- 用户端: http://localhost:8081
- 管理端API文档: http://localhost:8080/doc.html
- 用户端API文档: http://localhost:8081/doc.html
- Druid监控: http://localhost:8080/druid (admin/123456)

## 默认账号

### 管理端
- 用户名: admin
- 密码: 123456

### 用户端
- 用户名: user001
- 密码: 123456

## 代码生成器使用

1. 运行`monosome-generator`模块中的`CodeGenerator`类
2. 按提示输入数据库连接信息
3. 输入要生成的表名
4. 选择目标模块（管理端/用户端）
5. 自动生成对应的代码文件

## 打包部署

### 1. 打包命令
```bash
# 打包管理端
mvn clean package -pl monosome-manager -am

# 打包用户端
mvn clean package -pl monosome-using -am
```

### 2. 部署包结构
```
monosome-manager/
├── bin/                     # 启动脚本
│   ├── start.sh            # 启动脚本
│   ├── stop.sh             # 停止脚本
│   ├── restart.sh          # 重启脚本
│   └── start.bat           # Windows启动脚本
├── config/                  # 配置文件
│   └── application.yml
├── lib/                     # JAR文件
│   └── monosome-manager.jar
└── logs/                    # 日志目录
```

### 3. 启动命令
```bash
# Linux/Mac
./bin/start.sh

# Windows
bin\start.bat
```

## 开发指南

### 1. 新增模块
1. 在父POM中添加模块声明
2. 创建模块目录和POM文件
3. 添加必要的依赖

### 2. 新增实体
1. 继承`BaseEntity`基础实体类
2. 使用MyBatis Plus注解
3. 添加Swagger注解

### 3. 新增接口
1. 继承`BaseController`基础控制器
2. 使用统一返回结果`Result`
3. 添加Swagger注解

### 4. 异常处理
- 全局异常处理已在`BaseController`中实现
- 自定义异常可继承`RuntimeException`

### 5. 日志记录
- 使用SLF4J进行日志记录
- 日志配置在`application.yml`中

## 常见问题

### 1. 数据库连接失败
检查数据库连接配置和数据库服务状态

### 2. Redis连接失败
检查Redis服务状态和连接配置

### 3. 端口冲突
修改`application.yml`中的端口配置

### 4. 权限问题
检查JWT配置和Shiro配置

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 集成基础开发组件
- 实现权限管理功能
- 提供代码生成器
- 支持打包部署

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 作者: monosome
- 邮箱: <EMAIL>
- 项目地址: https://github.com/monosome/monosome
