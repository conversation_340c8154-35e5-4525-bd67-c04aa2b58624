package cn.monosome.common.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * 性能监控配置
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Configuration
@Order(1)
public class PerformanceConfig {

    /**
     * 监控Service层方法执行时间
     */
    @Around("execution(* cn.monosome.*.service.impl.*.*(..))")
    public Object monitorServicePerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 如果执行时间超过1秒，记录警告日志
            if (executionTime > 1000) {
                log.warn("慢方法检测 - {}.{} 执行时间: {} ms", className, methodName, executionTime);
            } else if (executionTime > 500) {
                log.info("方法执行时间 - {}.{} 执行时间: {} ms", className, methodName, executionTime);
            }
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            log.error("方法执行异常 - {}.{} 执行时间: {} ms, 异常: {}", 
                    className, methodName, executionTime, e.getMessage());
            throw e;
        }
    }

    /**
     * 监控Mapper层方法执行时间
     */
    @Around("execution(* cn.monosome.*.mapper.*.*(..))")
    public Object monitorMapperPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 如果SQL执行时间超过2秒，记录警告日志
            if (executionTime > 2000) {
                log.warn("慢SQL检测 - {}.{} 执行时间: {} ms", className, methodName, executionTime);
            } else if (executionTime > 1000) {
                log.info("SQL执行时间 - {}.{} 执行时间: {} ms", className, methodName, executionTime);
            }
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            log.error("SQL执行异常 - {}.{} 执行时间: {} ms, 异常: {}", 
                    className, methodName, executionTime, e.getMessage());
            throw e;
        }
    }
}
