package cn.monosome.common.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Knife4j配置
 *
 * <AUTHOR>
 */
@Configuration
public class Knife4jConfig {

    @Bean
    public GroupedOpenApi managerApi() {
        return GroupedOpenApi.builder()
                .group("管理端API")
                .packagesToScan("cn.monosome.manager.controller")
                .build();
    }

    @Bean
    public GroupedOpenApi usingApi() {
        return GroupedOpenApi.builder()
                .group("用户端API")
                .packagesToScan("cn.monosome.using.controller")
                .build();
    }

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Monosome API文档")
                        .description("Spring Boot快速开发脚手架 - API接口文档")
                        .version("1.0.0")
                        .contact(new Contact().name("monosome")))
                .addSecurityItem(new SecurityRequirement().addList("Authorization"))
                .components(new Components()
                        .addSecuritySchemes("Authorization", new SecurityScheme()
                                .name("Authorization")
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")));
    }
}
