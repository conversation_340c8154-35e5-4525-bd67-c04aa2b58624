package cn.monosome.common.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 多数据源配置（示例）
 * 
 * 注意：此配置默认未启用，如需使用多数据源，请：
 * 1. 在application.yml中配置多个数据源
 * 2. 取消此类的@Configuration注解注释
 * 3. 配置对应的Mapper扫描
 *
 * <AUTHOR>
 */
// @Configuration
public class MultiDataSourceConfig {

    /**
     * 主数据源
     */
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.druid.master")
    public DataSource masterDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    /**
     * 从数据源
     */
    @Bean
    @ConfigurationProperties("spring.datasource.druid.slave")
    public DataSource slaveDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    /**
     * 动态数据源
     */
    @Bean
    public DataSource dynamicDataSource() {
        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        Map<Object, Object> dataSourceMap = new HashMap<>();
        dataSourceMap.put("master", masterDataSource());
        dataSourceMap.put("slave", slaveDataSource());
        dynamicDataSource.setTargetDataSources(dataSourceMap);
        dynamicDataSource.setDefaultTargetDataSource(masterDataSource());
        return dynamicDataSource;
    }

    /**
     * 动态数据源实现
     */
    public static class DynamicDataSource extends org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource {
        
        private static final ThreadLocal<String> CONTEXT_HOLDER = new ThreadLocal<>();

        public static void setDataSourceType(String dataSourceType) {
            CONTEXT_HOLDER.set(dataSourceType);
        }

        public static String getDataSourceType() {
            return CONTEXT_HOLDER.get();
        }

        public static void clearDataSourceType() {
            CONTEXT_HOLDER.remove();
        }

        @Override
        protected Object determineCurrentLookupKey() {
            return getDataSourceType();
        }
    }

    /**
     * 数据源切换注解
     */
    public @interface DS {
        String value() default "master";
    }

    /**
     * 数据源切换AOP（需要配合@DataSource注解使用）
     */
    // @Aspect
    // @Component
    public static class DataSourceAspect {
        
        // @Around("@annotation(ds)")
        public Object around(org.aspectj.lang.ProceedingJoinPoint point, DS ds) throws Throwable {
            String dataSourceType = ds.value();
            DynamicDataSource.setDataSourceType(dataSourceType);
            try {
                return point.proceed();
            } finally {
                DynamicDataSource.clearDataSourceType();
            }
        }
    }
}
