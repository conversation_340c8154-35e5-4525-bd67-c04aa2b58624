package cn.monosome.common.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * JWT工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class JwtUtil {

    /**
     * 默认密钥
     */
    private static final String SECRET = "monosome-jwt-secret-key";

    /**
     * 默认过期时间（7天）
     */
    private static final long EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000L;

    /**
     * 生成token
     *
     * @param userId   用户ID
     * @param username 用户名
     * @return token
     */
    public static String createToken(Long userId, String username) {
        return createToken(userId, username, EXPIRE_TIME);
    }

    /**
     * 生成token
     *
     * @param userId     用户ID
     * @param username   用户名
     * @param expireTime 过期时间（毫秒）
     * @return token
     */
    public static String createToken(Long userId, String username, long expireTime) {
        try {
            Date date = new Date(System.currentTimeMillis() + expireTime);
            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            return JWT.create()
                    .withClaim("userId", userId)
                    .withClaim("username", username)
                    .withExpiresAt(date)
                    .sign(algorithm);
        } catch (Exception e) {
            log.error("生成token异常：", e);
            return null;
        }
    }

    /**
     * 验证token
     *
     * @param token token
     * @return 是否有效
     */
    public static boolean verify(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.error("token验证失败：", e);
            return false;
        }
    }

    /**
     * 获取用户ID
     *
     * @param token token
     * @return 用户ID
     */
    public static Long getUserId(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userId").asLong();
        } catch (JWTDecodeException e) {
            log.error("获取用户ID异常：", e);
            return null;
        }
    }

    /**
     * 获取用户名
     *
     * @param token token
     * @return 用户名
     */
    public static String getUsername(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("username").asString();
        } catch (JWTDecodeException e) {
            log.error("获取用户名异常：", e);
            return null;
        }
    }

    /**
     * 获取过期时间
     *
     * @param token token
     * @return 过期时间
     */
    public static Date getExpireTime(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt();
        } catch (JWTDecodeException e) {
            log.error("获取过期时间异常：", e);
            return null;
        }
    }

    /**
     * 判断token是否过期
     *
     * @param token token
     * @return 是否过期
     */
    public static boolean isExpired(String token) {
        Date expireTime = getExpireTime(token);
        if (expireTime == null) {
            return true;
        }
        return expireTime.before(new Date());
    }
}
