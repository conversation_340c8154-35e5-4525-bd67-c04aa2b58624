package cn.monosome.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.util.ListUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * Excel工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtil {

    /**
     * 导出Excel
     *
     * @param response  响应对象
     * @param data      数据列表
     * @param fileName  文件名
     * @param sheetName 工作表名
     * @param clazz     数据类型
     */
    public static <T> void exportExcel(HttpServletResponse response, List<T> data, String fileName, String sheetName, Class<T> clazz) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            EasyExcel.write(response.getOutputStream(), clazz)
                    .sheet(sheetName)
                    .doWrite(data);
        } catch (IOException e) {
            log.error("导出Excel异常：", e);
            throw new RuntimeException("导出Excel失败");
        }
    }

    /**
     * 读取Excel
     *
     * @param fileName 文件路径
     * @param clazz    数据类型
     * @return 数据列表
     */
    public static <T> List<T> readExcel(String fileName, Class<T> clazz) {
        List<T> list = ListUtils.newArrayList();
        try {
            EasyExcel.read(fileName, clazz, new PageReadListener<T>(dataList -> {
                list.addAll(dataList);
            })).sheet().doRead();
        } catch (Exception e) {
            log.error("读取Excel异常：", e);
            throw new RuntimeException("读取Excel失败");
        }
        return list;
    }

    /**
     * 读取Excel（带监听器）
     *
     * @param fileName     文件路径
     * @param clazz        数据类型
     * @param readListener 读取监听器
     */
    public static <T> void readExcel(String fileName, Class<T> clazz, PageReadListener<T> readListener) {
        try {
            EasyExcel.read(fileName, clazz, readListener).sheet().doRead();
        } catch (Exception e) {
            log.error("读取Excel异常：", e);
            throw new RuntimeException("读取Excel失败");
        }
    }
}
