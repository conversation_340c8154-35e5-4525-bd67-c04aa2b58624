package cn.monosome.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回状态码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 失败
     */
    ERROR(500, "操作失败"),

    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),

    /**
     * 未授权
     */
    UNAUTHORIZED(401, "未授权"),

    /**
     * 禁止访问
     */
    FORBIDDEN(403, "禁止访问"),

    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),

    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),

    /**
     * 请求超时
     */
    REQUEST_TIMEOUT(408, "请求超时"),

    /**
     * 用户名或密码错误
     */
    LOGIN_ERROR(1001, "用户名或密码错误"),

    /**
     * 用户已被禁用
     */
    USER_DISABLED(1002, "用户已被禁用"),

    /**
     * 用户不存在
     */
    USER_NOT_EXIST(1003, "用户不存在"),

    /**
     * Token无效
     */
    TOKEN_INVALID(1004, "Token无效"),

    /**
     * Token过期
     */
    TOKEN_EXPIRED(1005, "Token过期"),

    /**
     * 权限不足
     */
    PERMISSION_DENIED(1006, "权限不足"),

    /**
     * 数据已存在
     */
    DATA_EXIST(2001, "数据已存在"),

    /**
     * 数据不存在
     */
    DATA_NOT_EXIST(2002, "数据不存在"),

    /**
     * 数据操作失败
     */
    DATA_OPERATION_ERROR(2003, "数据操作失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 消息
     */
    private final String message;
}
