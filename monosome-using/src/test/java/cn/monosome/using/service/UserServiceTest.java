package cn.monosome.using.service;

import cn.monosome.using.entity.User;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户服务测试
 *
 * <AUTHOR>
 */
@SpringBootTest
class UserServiceTest {

    @Autowired
    private IUserService userService;

    @Test
    void testLogin() {
        // 测试登录
        String token = userService.login("user001", "123456");
        assertNotNull(token);
        System.out.println("登录成功，Token: " + token);
    }

    @Test
    void testRegister() {
        // 测试注册
        User user = new User();
        user.setUsername("testuser" + System.currentTimeMillis());
        user.setPassword("123456");
        user.setNickname("测试用户");
        user.setPhone("13800138000");
        user.setEmail("<EMAIL>");
        
        boolean result = userService.register(user);
        assertTrue(result);
        System.out.println("注册成功，用户ID: " + user.getId());
    }

    @Test
    void testSelectUserByUsername() {
        // 测试根据用户名查询用户
        User user = userService.selectUserByUsername("user001");
        assertNotNull(user);
        assertEquals("user001", user.getUsername());
        System.out.println("查询用户成功: " + user.getNickname());
    }

    @Test
    void testChangePassword() {
        // 先创建一个测试用户
        User user = new User();
        user.setUsername("changepasstest" + System.currentTimeMillis());
        user.setPassword("123456");
        user.setNickname("修改密码测试用户");
        userService.register(user);
        
        // 测试修改密码
        boolean result = userService.changePassword(user.getId(), "123456", "newpassword");
        assertTrue(result);
        System.out.println("密码修改成功");
    }
}
