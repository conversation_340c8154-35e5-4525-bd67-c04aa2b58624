package cn.monosome.using.service.impl;

import cn.monosome.using.entity.Product;
import cn.monosome.using.mapper.ProductMapper;
import cn.monosome.using.service.IProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {

}
