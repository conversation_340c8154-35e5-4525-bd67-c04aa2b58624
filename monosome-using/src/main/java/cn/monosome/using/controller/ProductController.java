package cn.monosome.using.controller;

import cn.monosome.common.base.BaseController;
import cn.monosome.common.result.Result;
import cn.monosome.using.entity.Product;
import cn.monosome.using.service.IProductService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 商品控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/product")
@Tag(name = "商品管理")
public class ProductController extends BaseController {

    @Autowired
    private IProductService productService;

    @GetMapping("/list")
    @Operation(summary = "商品列表")
    public Result<Page<Product>> list(@RequestParam(defaultValue = "1") Integer current,
                                      @RequestParam(defaultValue = "10") Integer size,
                                      @RequestParam(required = false) String name,
                                      @RequestParam(required = false) Long categoryId) {
        Page<Product> page = new Page<>(current, size);
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        
        // 只查询上架的商品
        queryWrapper.eq(Product::getStatus, 1);
        
        if (name != null && !name.trim().isEmpty()) {
            queryWrapper.like(Product::getName, name);
        }
        if (categoryId != null) {
            queryWrapper.eq(Product::getCategoryId, categoryId);
        }
        
        queryWrapper.orderByDesc(Product::getSort, Product::getCreateTime);
        
        Page<Product> result = productService.page(page, queryWrapper);
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取商品详情")
    public Result<Product> getById(@PathVariable Long id) {
        Product product = productService.getById(id);
        return product != null ? Result.success(product) : Result.error("商品不存在");
    }
}
