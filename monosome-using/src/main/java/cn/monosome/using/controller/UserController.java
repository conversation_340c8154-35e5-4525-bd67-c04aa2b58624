package cn.monosome.using.controller;

import cn.monosome.common.base.BaseController;
import cn.monosome.common.result.Result;
import cn.monosome.using.entity.User;
import cn.monosome.using.service.IUserService;
import cn.monosome.using.vo.LoginRequest;
import cn.monosome.using.vo.LoginResponse;
import cn.monosome.using.vo.ChangePasswordRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Tag(name = "用户管理")
public class UserController extends BaseController {

    @Autowired
    private IUserService userService;

    @PostMapping("/login")
    @Operation(summary = "用户登录")
    public Result<LoginResponse> login(@RequestBody @Validated LoginRequest request) {
        String token = userService.login(request.getUsername(), request.getPassword());
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        return Result.success("登录成功", response);
    }

    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public Result<String> register(@RequestBody @Validated User user) {
        boolean success = userService.register(user);
        return success ? Result.success("注册成功") : Result.error("注册失败");
    }

    @PostMapping("/changePassword")
    @Operation(summary = "修改密码")
    public Result<String> changePassword(@RequestBody @Validated ChangePasswordRequest request) {
        boolean success = userService.changePassword(request.getUserId(), request.getOldPassword(), request.getNewPassword());
        return success ? Result.success("密码修改成功") : Result.error("密码修改失败");
    }

    @GetMapping("/profile/{id}")
    @Operation(summary = "获取用户信息")
    public Result<User> getProfile(@PathVariable Long id) {
        User user = userService.getById(id);
        return user != null ? Result.success(user) : Result.error("用户不存在");
    }

    @PutMapping("/profile")
    @Operation(summary = "更新用户信息")
    public Result<String> updateProfile(@RequestBody @Validated User user) {
        boolean success = userService.updateById(user);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }
}
