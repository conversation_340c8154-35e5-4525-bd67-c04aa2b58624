package cn.monosome.using;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 用户端启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "cn.monosome")
@MapperScan("cn.monosome.using.mapper")
public class UsingApplication {

    public static void main(String[] args) {
        SpringApplication.run(UsingApplication.class, args);
        System.out.println("=================================");
        System.out.println("用户端启动成功！");
        System.out.println("接口文档地址：http://localhost:8081/doc.html");
        System.out.println("=================================");
    }
}
