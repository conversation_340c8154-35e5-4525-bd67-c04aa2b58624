package cn.monosome.using.entity;

import cn.monosome.common.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product")
@Schema(description = "商品")
public class Product extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "商品名称")
    @TableField("name")
    private String name;

    @Schema(description = "商品描述")
    @TableField("description")
    private String description;

    @Schema(description = "商品价格")
    @TableField("price")
    private BigDecimal price;

    @Schema(description = "商品图片")
    @TableField("image")
    private String image;

    @Schema(description = "商品分类ID")
    @TableField("category_id")
    private Long categoryId;

    @Schema(description = "库存数量")
    @TableField("stock")
    private Integer stock;

    @Schema(description = "状态（0下架 1上架）")
    @TableField("status")
    private Integer status;

    @Schema(description = "排序")
    @TableField("sort")
    private Integer sort;
}
